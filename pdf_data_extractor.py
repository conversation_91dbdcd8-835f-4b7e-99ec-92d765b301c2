from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser

import streamlit as st
import os
from dotenv import load_dotenv

# Try different PDF libraries
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    try:
        import pypdf as PyPDF2
        PDF_AVAILABLE = True
    except ImportError:
        PDF_AVAILABLE = False

# Load environment variables
load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGCHAIN_API_KEY")

def extract_text_from_pdf(pdf_file):
    """Extract text from uploaded PDF file"""
    if not PDF_AVAILABLE:
        return "Error: PDF library not available. Please install PyPDF2."

    try:
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        text = ""
        for page in pdf_reader.pages:
            page_text = page.extract_text()
            if page_text:
                text += page_text + "\n"
        return text
    except Exception as e:
        return f"Error reading PDF: {str(e)}"

# Prompt template for PDF data extraction
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a PDF data extraction expert. You analyze PDF content and answer questions about the data.
    Provide accurate, detailed answers based only on the PDF content. If information is not in the PDF, clearly state that.
    Format your response clearly and professionally."""),
    ("user", "PDF Content: {pdf_content}\n\nQuestion: {question}")
])

# Streamlit App
st.set_page_config(page_title="PDF Data Extractor", page_icon="📄", layout="wide")

st.title("📄 PDF Data Extractor Chatbot")
st.markdown("**Upload a PDF file and ask questions to extract specific data!**")

# Check if PDF library is available
if not PDF_AVAILABLE:
    st.error("❌ PDF library not found. Installing PyPDF2...")
    if st.button("Install PyPDF2"):
        os.system("pip install PyPDF2")
        st.rerun()

# Initialize session state
if 'pdf_content' not in st.session_state:
    st.session_state.pdf_content = ""
if 'pdf_uploaded' not in st.session_state:
    st.session_state.pdf_uploaded = False

# Sidebar for PDF upload
with st.sidebar:
    st.header("📁 Upload PDF")
    uploaded_pdf = st.file_uploader(
        "Choose a PDF file",
        type=['pdf'],
        help="Upload a PDF file to extract data from"
    )

    if uploaded_pdf and PDF_AVAILABLE:
        if st.button("📖 Process PDF", type="primary"):
            with st.spinner("Reading PDF..."):
                pdf_text = extract_text_from_pdf(uploaded_pdf)
                if pdf_text and not pdf_text.startswith("Error"):
                    st.session_state.pdf_content = pdf_text
                    st.session_state.pdf_uploaded = True
                    st.success("✅ PDF processed successfully!")
                    st.info(f"📊 Extracted {len(pdf_text.split())} words")
                else:
                    st.error("❌ Failed to extract text from PDF")
                    st.session_state.pdf_uploaded = False

# Main content
if st.session_state.pdf_uploaded:
    st.success("📄 PDF is ready for questions!")

    # Sample questions
    st.subheader("💡 Sample Questions")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📝 Summarize"):
            st.session_state.current_question = "Provide a comprehensive summary of this document"
        if st.button("👥 Extract names"):
            st.session_state.current_question = "Extract all person names mentioned in this document"
        if st.button("📅 Find dates"):
            st.session_state.current_question = "Extract all dates mentioned in this document"

    with col2:
        if st.button("💰 Extract amounts"):
            st.session_state.current_question = "Extract all numbers, amounts, or financial figures from this document"
        if st.button("📧 Contact info"):
            st.session_state.current_question = "Extract all contact information like emails, phone numbers, addresses"
        if st.button("🏢 Organizations"):
            st.session_state.current_question = "Extract all company names or organization names mentioned"

    with col3:
        if st.button("📋 Key points"):
            st.session_state.current_question = "What are the main key points or important information in this document?"
        if st.button("📊 Data/statistics"):
            st.session_state.current_question = "Extract all data, statistics, or metrics from this document"
        if st.button("🎯 Main topic"):
            st.session_state.current_question = "What is the main topic or subject of this document?"

    # Question input
    st.markdown("---")
    st.subheader("🤖 Ask Your Question")

    # Use sample question if selected
    default_question = st.session_state.get('current_question', '')
    question = st.text_area(
        "What would you like to extract from the PDF?",
        value=default_question,
        height=100,
        placeholder="Example: Extract all names and their roles mentioned in this document"
    )

    if st.button("🔍 Extract Data", type="primary") and question:
        with st.spinner("🤖 AI is analyzing the PDF..."):
            try:
                # Create LLM
                llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
                output_parser = StrOutputParser()
                chain = prompt | llm | output_parser

                # Get response
                response = chain.invoke({
                    'pdf_content': st.session_state.pdf_content[:8000],  # Limit content size
                    'question': question
                })

                # Display response
                st.markdown("### 📋 Extracted Information:")
                st.markdown(response)

                # Show PDF preview
                with st.expander("📖 PDF Content Preview"):
                    st.text(st.session_state.pdf_content[:1500] + "..." if len(st.session_state.pdf_content) > 1500 else st.session_state.pdf_content)

            except Exception as e:
                st.error(f"❌ Error: {str(e)}")
                st.info("💡 Make sure your OpenAI API key is set correctly in the .env file")

else:
    # Instructions when no PDF is uploaded
    st.info("👆 Please upload a PDF file from the sidebar to start extracting data!")

    st.markdown("### 🚀 How to Use:")
    st.markdown("""
    1. **Upload PDF** 📁 - Click on sidebar and select your PDF file
    2. **Process PDF** 📖 - Click "Process PDF" to extract text
    3. **Ask Questions** 🤖 - Use sample questions or type your own
    4. **Get Results** ✨ - AI will extract the requested data
    """)

    st.markdown("### 📝 Example Questions:")
    st.markdown("""
    - "Extract all names and their designations"
    - "What are the key financial figures?"
    - "Summarize the main points of this document"
    - "Extract all dates and events mentioned"
    - "Find all contact information"
    - "What companies are mentioned in this document?"
    """)

# Footer
st.markdown("---")
st.markdown("**💡 Tip:** Ask specific questions to get better results from your PDF!")
