import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import PyPDF2

# Load environment variables
load_dotenv()

# Set API keys
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")

# Function to extract text from PDF
def extract_text_from_pdf(pdf_path):
    try:
        with open(pdf_path, "rb") as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text
    except Exception as e:
        return f"Error: {str(e)}"

# Combine text from multiple PDFs
def extract_from_multiple_pdfs(pdf_paths):
    combined_text = ""
    for path in pdf_paths:
        combined_text += extract_text_from_pdf(path) + "\n"
    return combined_text

# Prepare the LangChain prompt
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a PDF data extraction expert. Provide accurate answers based only on the PDF content below.
    If the answer is not in the content, say so clearly."""),
    ("user", "PDF Content: {pdf_content}\n\nQuestion: {question}")
])

# Main function
def ask_question_about_pdfs(pdf_paths, question):
    pdf_content = extract_from_multiple_pdfs(pdf_paths)[:8000]  # limit size
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    chain = prompt | llm | StrOutputParser()
    response = chain.invoke({
        "pdf_content": pdf_content,
        "question": question
    })
    return response

# Example usage
if __name__ == "__main__":
    # Add your local PDF file paths
    pdf_files = ["invoice1.pdf", "invoice2.pdf", "report.pdf"]
    user_question = "Summarize the key financial information from these documents."

    result = ask_question_about_pdfs(pdf_files, user_question)
    print("\n--- Extracted Answer ---\n")
    print(result)
