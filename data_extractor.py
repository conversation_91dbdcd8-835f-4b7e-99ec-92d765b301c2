from langchain_openai import Chat<PERSON>penAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings

import streamlit as st
import os
import pandas as pd
import PyPDF2
import docx
from io import StringIO
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

os.environ["OPENAI_API_KEY"]=os.getenv("OPENAI_API_KEY")
## Langmith tracking
os.environ["LANGCHAIN_TRACING_V2"]="true"
os.environ["LANGCHAIN_API_KEY"]=os.getenv("LANGCHAIN_API_KEY")

# Function to extract text from different file types
def extract_text_from_file(uploaded_file):
    text = ""
    file_type = uploaded_file.type
    
    try:
        if file_type == "text/plain":
            # Text file
            stringio = StringIO(uploaded_file.getvalue().decode("utf-8"))
            text = stringio.read()
        
        elif file_type == "application/pdf":
            # PDF file
            pdf_reader = PyPDF2.PdfReader(uploaded_file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        
        elif file_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            # Word document
            doc = docx.Document(uploaded_file)
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
        
        elif file_type in ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]:
            # Excel file
            df = pd.read_excel(uploaded_file)
            text = df.to_string()
        
        elif file_type == "text/csv":
            # CSV file
            df = pd.read_csv(uploaded_file)
            text = df.to_string()
        
        else:
            # Try to read as text
            try:
                text = uploaded_file.getvalue().decode("utf-8")
            except:
                text = "Could not extract text from this file type."
                
    except Exception as e:
        text = f"Error extracting text: {str(e)}"
    
    return text

# Function to create vector store from documents
def create_vector_store(texts):
    try:
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )
        chunks = text_splitter.split_text(texts)
        
        if not chunks:
            return None
            
        embeddings = OpenAIEmbeddings()
        vector_store = FAISS.from_texts(chunks, embeddings)
        return vector_store
    except Exception as e:
        st.error(f"Error creating vector store: {str(e)}")
        return None

## Prompt Template for data extraction
prompt = ChatPromptTemplate.from_messages(
    [
        ("system", """You are an expert data extraction assistant. You have access to document content and can answer questions about the data in those documents. 
        Please provide accurate and detailed answers based on the document content. If the information is not available in the documents, clearly state that.
        Format your response in a clear and organized manner."""),
        ("user", "Context: {context}\n\nQuestion: {question}")
    ]
)

## Streamlit App
st.set_page_config(page_title="Smart Data Extractor", page_icon="📄", layout="wide")

st.title('📄 Smart Data Extractor with AI')
st.markdown("**Upload multiple files and ask questions to extract specific data from them!**")

# Sidebar for file upload
with st.sidebar:
    st.header("📁 File Upload")
    uploaded_files = st.file_uploader(
        "Choose files to upload", 
        type=['txt', 'pdf', 'docx', 'xlsx', 'xls', 'csv'],
        accept_multiple_files=True,
        help="Supported formats: TXT, PDF, DOCX, XLSX, XLS, CSV"
    )
    
    if uploaded_files:
        st.success(f"✅ {len(uploaded_files)} file(s) uploaded")
        for file in uploaded_files:
            st.write(f"• {file.name}")

# Initialize session state
if 'vector_store' not in st.session_state:
    st.session_state.vector_store = None
if 'documents_processed' not in st.session_state:
    st.session_state.documents_processed = False
if 'file_contents' not in st.session_state:
    st.session_state.file_contents = {}

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    # Process uploaded files
    if uploaded_files:
        if st.button("🔄 Process Files", type="primary"):
            with st.spinner('Processing files...'):
                all_text = ""
                file_info = []
                st.session_state.file_contents = {}
                
                for uploaded_file in uploaded_files:
                    try:
                        text = extract_text_from_file(uploaded_file)
                        if text and text.strip():
                            all_text += f"\n\n--- Content from {uploaded_file.name} ---\n\n{text}"
                            st.session_state.file_contents[uploaded_file.name] = text
                            file_info.append({
                                "File Name": uploaded_file.name,
                                "File Type": uploaded_file.type,
                                "File Size": f"{uploaded_file.size} bytes",
                                "Status": "✅ Processed"
                            })
                        else:
                            file_info.append({
                                "File Name": uploaded_file.name,
                                "File Type": uploaded_file.type,
                                "File Size": f"{uploaded_file.size} bytes",
                                "Status": "❌ No content extracted"
                            })
                    except Exception as e:
                        file_info.append({
                            "File Name": uploaded_file.name,
                            "File Type": uploaded_file.type,
                            "File Size": f"{uploaded_file.size} bytes",
                            "Status": f"❌ Error: {str(e)}"
                        })
                
                if all_text.strip():
                    # Create vector store
                    st.session_state.vector_store = create_vector_store(all_text)
                    if st.session_state.vector_store:
                        st.session_state.documents_processed = True
                        st.success(f"🎉 Successfully processed {len([f for f in file_info if '✅' in f['Status']])} files!")
                    else:
                        st.error("Failed to create vector store from documents.")
                else:
                    st.error("No text content could be extracted from the uploaded files.")
                
                # Display file information
                if file_info:
                    st.subheader("📋 Processing Results")
                    df = pd.DataFrame(file_info)
                    st.dataframe(df, use_container_width=True)

with col2:
    # Sample questions
    st.subheader("💡 Sample Questions")
    sample_questions = [
        "What is the main topic of these documents?",
        "Extract all names mentioned in the files",
        "What are the key statistics or numbers?",
        "Summarize the content of each file",
        "What dates are mentioned in the documents?",
        "Extract contact information",
        "Find specific information about [topic]",
        "What are the main conclusions?"
    ]
    
    for i, question in enumerate(sample_questions):
        if st.button(question, key=f"sample_{i}", use_container_width=True):
            st.session_state.sample_question = question

# Question input and processing
if st.session_state.documents_processed:
    st.markdown("---")
    st.subheader("🤖 Ask Questions About Your Data")
    
    # Use sample question if selected
    default_question = st.session_state.get('sample_question', '')
    question = st.text_input(
        "Enter your question about the uploaded files:",
        value=default_question,
        placeholder="e.g., What are the main points discussed in these documents?"
    )
    
    if question:
        with st.spinner('🔍 Extracting data...'):
            try:
                # Create retrieval chain
                llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
                
                # Get relevant documents
                retriever = st.session_state.vector_store.as_retriever(search_kwargs={"k": 3})
                relevant_docs = retriever.get_relevant_documents(question)
                
                if relevant_docs:
                    # Combine relevant context
                    context = "\n\n".join([doc.page_content for doc in relevant_docs])
                    
                    # Create the chain
                    output_parser = StrOutputParser()
                    chain = prompt | llm | output_parser
                    
                    # Get response
                    response = chain.invoke({
                        'context': context,
                        'question': question
                    })
                    
                    # Display response
                    st.markdown("### 📋 Extracted Information:")
                    st.markdown(response)
                    
                    # Show source context
                    with st.expander("📖 Source Context Used"):
                        for i, doc in enumerate(relevant_docs, 1):
                            st.markdown(f"**Source {i}:**")
                            st.text(doc.page_content[:500] + "..." if len(doc.page_content) > 500 else doc.page_content)
                            if i < len(relevant_docs):
                                st.markdown("---")
                else:
                    st.warning("No relevant information found for your question.")
                        
            except Exception as e:
                st.error(f"Error processing question: {str(e)}")
                st.info("Please make sure your OpenAI API key is correctly set in the .env file.")

else:
    st.info("👆 Please upload files and click 'Process Files' to start extracting data!")
    
    # Show supported file types
    with st.expander("📄 Supported File Types"):
        st.markdown("""
        - **Text Files**: .txt
        - **PDF Documents**: .pdf  
        - **Word Documents**: .docx
        - **Excel Files**: .xlsx, .xls
        - **CSV Files**: .csv
        """)

# Footer
st.markdown("---")
st.markdown("**💡 Tip:** Upload multiple related documents to get comprehensive answers across all your files!")
